#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/jest@29.7.0_@types+node@20._48116a6a49ef79ed6dd6a3d67063e6a6/node_modules/jest/bin/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/jest@29.7.0_@types+node@20._48116a6a49ef79ed6dd6a3d67063e6a6/node_modules/jest/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/jest@29.7.0_@types+node@20._48116a6a49ef79ed6dd6a3d67063e6a6/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/jest@29.7.0_@types+node@20._48116a6a49ef79ed6dd6a3d67063e6a6/node_modules/jest/bin/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/jest@29.7.0_@types+node@20._48116a6a49ef79ed6dd6a3d67063e6a6/node_modules/jest/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/jest@29.7.0_@types+node@20._48116a6a49ef79ed6dd6a3d67063e6a6/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../jest/bin/jest.js" "$@"
else
  exec node  "$basedir/../jest/bin/jest.js" "$@"
fi
