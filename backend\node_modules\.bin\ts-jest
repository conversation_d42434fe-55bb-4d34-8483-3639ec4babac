#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@_e6c7089a1828304dc6bcc90a90d99ec6/node_modules/ts-jest/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@_e6c7089a1828304dc6bcc90a90d99ec6/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@_e6c7089a1828304dc6bcc90a90d99ec6/node_modules/ts-jest/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@_e6c7089a1828304dc6bcc90a90d99ec6/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-jest/cli.js" "$@"
else
  exec node  "$basedir/../ts-jest/cli.js" "$@"
fi
