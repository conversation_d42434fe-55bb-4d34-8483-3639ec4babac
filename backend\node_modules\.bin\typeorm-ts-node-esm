#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/typeorm@0.3.25_ioredis@5.6._a4bb3740dac8687fc6b9eb3073406139/node_modules/typeorm/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/typeorm@0.3.25_ioredis@5.6._a4bb3740dac8687fc6b9eb3073406139/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/typeorm@0.3.25_ioredis@5.6._a4bb3740dac8687fc6b9eb3073406139/node_modules/typeorm/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/typeorm@0.3.25_ioredis@5.6._a4bb3740dac8687fc6b9eb3073406139/node_modules:/mnt/d/Users/<USER>/SeewoTool/量化积分管理系统/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../typeorm/cli-ts-node-esm.js" "$@"
else
  exec node  "$basedir/../typeorm/cli-ts-node-esm.js" "$@"
fi
