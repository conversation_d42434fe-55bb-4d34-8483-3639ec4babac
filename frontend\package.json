{"name": "quantitative-points-system", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^2.6.0", "@ant-design/icons": "^5.0.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@prisma/client": "^6.11.1", "ahooks": "^3.9.0", "antd": "^5.0.0", "axios": "^1.10.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.0.0", "vue": "^3.5.17"}, "devDependencies": {"@types/axios": "^0.14.4", "@types/node": "^24.0.15", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-vue": "4.5.2", "@vue/tsconfig": "^0.7.0", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "less": "^4.3.0", "rollup-plugin-visualizer": "^6.0.3", "terser": "^5.43.1", "typescript": "^5.2.2", "vite": "4.5.2", "vite-plugin-compression": "^0.5.1"}}