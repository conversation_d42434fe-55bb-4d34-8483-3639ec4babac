import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { createApp } from 'vue';
import Toast from '../components/Toast.vue';

// 创建一个显示消息的函数
const showMessage = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  // 创建一个新的div元素作为挂载点
  const mountPoint = document.createElement('div');
  document.body.appendChild(mountPoint);

  // 创建一个新的Vue应用实例
  const app = createApp(Toast, {
    message,
    type,
    duration: 3000,
    onClose: () => {
      // 消息关闭后清理DOM
      app.unmount();
      document.body.removeChild(mountPoint);
    }
  });

  // 挂载应用
  app.mount(mountPoint);
};

// 创建axios实例
const instance: AxiosInstance = axios.create({
  baseURL: '/api', // 使用相对路径，让Vite的代理处理
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    const deviceId = localStorage.getItem('deviceId');

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    if (deviceId) {
      config.headers['Device-Id'] = deviceId;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    return response.data;
  },
  async (error) => {
    if (error.response) {
      const { status, data } = error.response;
      const originalRequest = error.config;

      // 处理token过期
      if (status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          const refreshToken = localStorage.getItem('refreshToken');
          const deviceId = localStorage.getItem('deviceId');

          if (refreshToken && deviceId) {
            // 尝试刷新token
            const response = await axios.post('/api/auth/refresh', null, {
              headers: {
                'Refresh-Token': refreshToken,
                'Device-Id': deviceId,
              },
            });

            const { access_token, refresh_token } = response.data;
            
            // 更新存储的token
            localStorage.setItem('token', access_token);
            localStorage.setItem('refreshToken', refresh_token);

            // 重试原始请求
            originalRequest.headers.Authorization = `Bearer ${access_token}`;
            return axios(originalRequest);
          }
        } catch (refreshError) {
          // 刷新token失败，需要重新登录
          showMessage('登录已过期，请重新登录', 'error');
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('deviceId');
          localStorage.removeItem('user');
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      }

      switch (status) {
        case 400:
          showMessage(data.message || '请求参数错误', 'error');
          break;
        case 401:
          showMessage('未登录或登录已过期', 'error');
          break;
        case 403:
          showMessage('没有权限访问该资源', 'error');
          break;
        case 404:
          showMessage('请求的资源不存在', 'error');
          break;
        case 500:
          showMessage('服务器错误', 'error');
          break;
        default:
          showMessage('未知错误', 'error');
      }
    } else if (error.request) {
      showMessage('网络错误，请检查网络连接', 'error');
    } else {
      showMessage('请求配置错误', 'error');
    }

    return Promise.reject(error);
  }
);

export interface RequestConfig extends AxiosRequestConfig {
  skipErrorHandler?: boolean;
}

const request = {
  get: <T = any>(url: string, config?: RequestConfig): Promise<T> => {
    return instance.get<T>(url, config).then(response => response as unknown as T);
  },

  post: <T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> => {
    return instance.post<T>(url, data, config).then(response => response as unknown as T);
  },

  put: <T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> => {
    return instance.put<T>(url, data, config).then(response => response as unknown as T);
  },

  delete: <T = any>(url: string, config?: RequestConfig): Promise<T> => {
    return instance.delete<T>(url, config).then(response => response as unknown as T);
  },

  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return instance.patch(url, data, config);
  },
};

export default request;
export { request };

export interface Response<T = any> {
  code: number;
  data: T;
  message: string;
}

export async function get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
  const response = await instance.get<T>(url, config);
  return response as unknown as T;
}

export async function post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  const response = await instance.post<T>(url, data, config);
  return response as unknown as T;
}

export async function put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  const response = await instance.put<T>(url, data, config);
  return response as unknown as T;
}

export async function del<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
  const response = await instance.delete<T>(url, config);
  return response as unknown as T;
} 