hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.0':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@fast-csv/format@5.0.2':
    '@fast-csv/format': private
  '@fast-csv/parse@5.0.2':
    '@fast-csv/parse': private
  '@ioredis/commands@1.2.0':
    '@ioredis/commands': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@29.7.0':
    '@jest/console': private
  '@jest/core@29.7.0(ts-node@10.9.2(@types/node@20.19.4)(typescript@5.8.3))':
    '@jest/core': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/expect-utils@29.7.0':
    '@jest/expect-utils': private
  '@jest/expect@29.7.0':
    '@jest/expect': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/globals@29.7.0':
    '@jest/globals': private
  '@jest/pattern@30.0.1':
    '@jest/pattern': private
  '@jest/reporters@29.7.0':
    '@jest/reporters': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/source-map@29.6.3':
    '@jest/source-map': private
  '@jest/test-result@29.7.0':
    '@jest/test-result': private
  '@jest/test-sequencer@29.7.0':
    '@jest/test-sequencer': private
  '@jest/transform@30.0.4':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@lukeed/csprng@1.1.0':
    '@lukeed/csprng': private
  '@microsoft/tsdoc@0.15.1':
    '@microsoft/tsdoc': private
  '@nestjs/mapped-types@2.1.0(@nestjs/common@11.1.3(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)':
    '@nestjs/mapped-types': private
  '@nuxt/opencollective@0.4.1':
    '@nuxt/opencollective': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@prisma/config@6.11.1':
    '@prisma/config': private
  '@prisma/debug@6.11.1':
    '@prisma/debug': private
  '@prisma/engines-version@6.11.1-1.f40f79ec31188888a2e33acda0ecc8fd10a853a9':
    '@prisma/engines-version': private
  '@prisma/engines@6.11.1':
    '@prisma/engines': private
  '@prisma/fetch-engine@6.11.1':
    '@prisma/fetch-engine': private
  '@prisma/get-platform@6.11.1':
    '@prisma/get-platform': private
  '@scarf/scarf@1.4.0':
    '@scarf/scarf': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@sqltools/formatter@1.2.5':
    '@sqltools/formatter': private
  '@streamparser/json@0.0.6':
    '@streamparser/json': private
  '@swc/helpers@0.5.17':
    '@swc/helpers': private
  '@tokenizer/inflate@0.2.7':
    '@tokenizer/inflate': private
  '@tokenizer/token@0.3.0':
    '@tokenizer/token': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/express-serve-static-core@4.19.6':
    '@types/express-serve-static-core': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/validator@13.15.2':
    '@types/validator': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  accepts@1.3.8:
    accepts: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@5.2.0:
    ansi-styles: private
  ansis@3.17.0:
    ansis: private
  anymatch@3.1.3:
    anymatch: private
  app-root-path@3.1.0:
    app-root-path: private
  append-field@1.0.0:
    append-field: private
  archiver-utils@2.1.0:
    archiver-utils: private
  archiver@5.3.2:
    archiver: private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  array-flatten@1.1.1:
    array-flatten: private
  async@3.2.6:
    async: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  aws-ssl-profiles@1.1.2:
    aws-ssl-profiles: private
  babel-jest@30.0.4(@babel/core@7.28.0):
    babel-jest: private
  babel-plugin-istanbul@7.0.0:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@30.0.1:
    babel-plugin-jest-hoist: private
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.28.0):
    babel-preset-current-node-syntax: private
  babel-preset-jest@30.0.1(@babel/core@7.28.0):
    babel-preset-jest: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  big-integer@1.6.52:
    big-integer: private
  binary-extensions@2.3.0:
    binary-extensions: private
  binary@0.3.0:
    binary: private
  bl@4.1.0:
    bl: private
  bluebird@3.4.7:
    bluebird: private
  body-parser@1.20.3:
    body-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  brotli@1.3.3:
    brotli: private
  browserslist@4.25.1:
    browserslist: private
  bs-logger@0.2.6:
    bs-logger: private
  bser@2.1.1:
    bser: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer-indexof-polyfill@1.0.2:
    buffer-indexof-polyfill: private
  buffer@6.0.3:
    buffer: private
  buffers@0.1.1:
    buffers: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001726:
    caniuse-lite: private
  chainsaw@0.1.0:
    chainsaw: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  chokidar@3.6.0:
    chokidar: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  cliui@8.0.1:
    cliui: private
  clone@2.1.2:
    clone: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  co@4.6.0:
    co: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  commander@6.2.1:
    commander: private
  compress-commons@4.1.2:
    compress-commons: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@2.0.0:
    concat-stream: private
  consola@3.4.2:
    consola: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  core-util-is@1.0.3:
    core-util-is: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@4.0.3:
    crc32-stream: private
  create-jest@29.7.0(@types/node@20.19.4)(ts-node@10.9.2(@types/node@20.19.4)(typescript@5.8.3)):
    create-jest: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypto-js@4.2.0:
    crypto-js: private
  dayjs@1.11.13:
    dayjs: private
  debug@2.6.9:
    debug: private
  dedent@1.6.0:
    dedent: private
  deepmerge@4.3.1:
    deepmerge: private
  define-data-property@1.1.4:
    define-data-property: private
  denque@2.1.0:
    denque: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-newline@3.1.0:
    detect-newline: private
  dfa@1.2.0:
    dfa: private
  diff-sequences@29.6.3:
    diff-sequences: private
  diff@4.0.2:
    diff: private
  dotenv-expand@12.0.1:
    dotenv-expand: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer2@0.1.4:
    duplexer2: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.179:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.5:
    end-of-stream: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@2.0.0:
    escape-string-regexp: private
  esprima@4.0.1:
    esprima: private
  etag@1.8.1:
    etag: private
  eventemitter2@6.4.9:
    eventemitter2: private
  execa@5.1.1:
    execa: private
  exit@0.1.2:
    exit: private
  expect@29.7.0:
    expect: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fflate@0.8.2:
    fflate: private
  file-type@21.0.0:
    file-type: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-up@4.1.0:
    find-up: private
  fontkit@2.0.4:
    fontkit: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  fs-constants@1.0.0:
    fs-constants: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  fstream@1.0.12:
    fstream: private
  function-bind@1.1.2:
    function-bind: private
  generate-function@2.3.1:
    generate-function: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  glob-parent@5.1.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@3.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  html-escaper@2.0.2:
    html-escaper: private
  http-errors@2.0.0:
    http-errors: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  immediate@3.0.6:
    immediate: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-promise@4.0.0:
    is-promise: private
  is-property@1.0.2:
    is-property: private
  is-stream@2.0.1:
    is-stream: private
  is-typed-array@1.1.15:
    is-typed-array: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  iterare@1.2.1:
    iterare: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jest-changed-files@29.7.0:
    jest-changed-files: private
  jest-circus@29.7.0:
    jest-circus: private
  jest-cli@29.7.0(@types/node@20.19.4)(ts-node@10.9.2(@types/node@20.19.4)(typescript@5.8.3)):
    jest-cli: private
  jest-config@29.7.0(@types/node@20.19.4)(ts-node@10.9.2(@types/node@20.19.4)(typescript@5.8.3)):
    jest-config: private
  jest-diff@29.7.0:
    jest-diff: private
  jest-docblock@29.7.0:
    jest-docblock: private
  jest-each@29.7.0:
    jest-each: private
  jest-environment-node@29.7.0:
    jest-environment-node: private
  jest-get-type@29.6.3:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-leak-detector@29.7.0:
    jest-leak-detector: private
  jest-matcher-utils@29.7.0:
    jest-matcher-utils: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-resolve-dependencies@29.7.0:
    jest-resolve-dependencies: private
  jest-resolve@29.7.0:
    jest-resolve: private
  jest-runner@29.7.0:
    jest-runner: private
  jest-runtime@29.7.0:
    jest-runtime: private
  jest-snapshot@29.7.0:
    jest-snapshot: private
  jest-util@30.0.2:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-watcher@29.7.0:
    jest-watcher: private
  jest-worker@29.7.0:
    jest-worker: private
  jiti@2.4.2:
    jiti: private
  jpeg-exif@1.1.4:
    jpeg-exif: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json5@2.2.3:
    json5: private
  jszip@3.10.1:
    jszip: private
  jwa@1.4.2:
    jwa: private
  jws@3.2.2:
    jws: private
  kleur@3.0.3:
    kleur: private
  lazystream@1.0.1:
    lazystream: private
  leven@3.1.0:
    leven: private
  libphonenumber-js@1.12.9:
    libphonenumber-js: private
  lie@3.3.0:
    lie: private
  linebreak@1.1.0:
    linebreak: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  listenercount@1.0.1:
    listenercount: private
  load-esm@1.0.2:
    load-esm: private
  locate-path@5.0.0:
    locate-path: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.difference@4.5.0:
    lodash.difference: private
  lodash.escaperegexp@4.1.2:
    lodash.escaperegexp: private
  lodash.flatten@4.4.0:
    lodash.flatten: private
  lodash.get@4.4.2:
    lodash.get: private
  lodash.groupby@4.6.0:
    lodash.groupby: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isarguments@3.1.0:
    lodash.isarguments: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isequal@4.5.0:
    lodash.isequal: private
  lodash.isfunction@3.0.9:
    lodash.isfunction: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnil@4.0.0:
    lodash.isnil: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.isundefined@3.0.1:
    lodash.isundefined: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash.union@4.6.0:
    lodash.union: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash@4.17.21:
    lodash: private
  long@5.3.2:
    long: private
  lru-cache@7.18.3:
    lru-cache: private
  lru.min@1.1.2:
    lru.min: private
  make-dir@4.0.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  makeerror@1.0.12:
    makeerror: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mkdirp@0.5.6:
    mkdirp: private
  ms@2.1.3:
    ms: private
  multer@2.0.1:
    multer: private
  named-placeholders@1.1.3:
    named-placeholders: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.3:
    negotiator: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@4.0.1:
    npm-run-path: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@4.1.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  pako@1.0.11:
    pako: private
  parse-json@5.2.0:
    parse-json: private
  parseurl@1.3.3:
    parseurl: private
  passport-strategy@1.0.0:
    passport-strategy: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@8.2.0:
    path-to-regexp: private
  pause@0.0.1:
    pause: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  png-js@1.0.0:
    png-js: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  pretty-format@29.7.0:
    pretty-format: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  prompts@2.4.2:
    prompts: private
  proxy-addr@2.0.7:
    proxy-addr: private
  pstree.remy@1.1.8:
    pstree.remy: private
  pure-rand@6.1.0:
    pure-rand: private
  qs@6.13.0:
    qs: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  react-is@18.3.1:
    react-is: private
  readable-stream@3.6.2:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  readdirp@3.6.0:
    readdirp: private
  redis-errors@1.2.0:
    redis-errors: private
  redis-parser@3.0.0:
    redis-parser: private
  reflect-metadata@0.2.2:
    reflect-metadata: private
  require-directory@2.1.1:
    require-directory: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  restructure@3.0.2:
    restructure: private
  rimraf@2.7.1:
    rimraf: private
  router@2.2.0:
    router: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  saxes@5.0.1:
    saxes: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  seq-queue@0.0.5:
    seq-queue: private
  serve-static@1.16.2:
    serve-static: private
  set-function-length@1.2.2:
    set-function-length: private
  setimmediate@1.0.5:
    setimmediate: private
  setprototypeof@1.2.0:
    setprototypeof: private
  sha.js@2.4.12:
    sha.js: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  source-map-support@0.5.13:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  sprintf-js@1.0.3:
    sprintf-js: private
  sql-highlight@6.1.0:
    sql-highlight: private
  sqlstring@2.3.3:
    sqlstring: private
  stack-utils@2.0.6:
    stack-utils: private
  standard-as-callback@2.1.0:
    standard-as-callback: private
  statuses@2.0.1:
    statuses: private
  streamsearch@1.1.0:
    streamsearch: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strtok3@10.3.1:
    strtok3: private
  supports-color@5.5.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swagger-ui-dist@5.21.0:
    swagger-ui-dist: private
  tar-stream@2.2.0:
    tar-stream: private
  test-exclude@6.0.0:
    test-exclude: private
  tiny-inflate@1.0.3:
    tiny-inflate: private
  tmp@0.2.3:
    tmp: private
  tmpl@1.0.5:
    tmpl: private
  to-buffer@1.2.1:
    to-buffer: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  token-types@6.0.3:
    token-types: private
  touch@3.1.1:
    touch: private
  traverse@0.3.9:
    traverse: private
  tslib@2.8.1:
    tslib: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@4.41.0:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typedarray@0.0.6:
    typedarray: private
  uid@2.0.2:
    uid: private
  uint8array-extras@1.4.0:
    uint8array-extras: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@6.21.0:
    undici-types: private
  unicode-properties@1.4.1:
    unicode-properties: private
  unicode-trie@2.0.0:
    unicode-trie: private
  unpipe@1.0.0:
    unpipe: private
  unzipper@0.10.14:
    unzipper: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utils-merge@1.0.1:
    utils-merge: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  validator@13.15.15:
    validator: private
  vary@1.1.2:
    vary: private
  walker@1.0.8:
    walker: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@5.0.1:
    write-file-atomic: private
  xmlchars@2.2.0:
    xmlchars: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zip-stream@4.1.1:
    zip-stream: private
ignoredBuilds:
  - '@nestjs/core'
  - '@scarf/scarf'
  - '@prisma/engines'
  - prisma
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Sun, 20 Jul 2025 10:37:08 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\Users\program\SeewoTool\量化积分管理系统\backend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
